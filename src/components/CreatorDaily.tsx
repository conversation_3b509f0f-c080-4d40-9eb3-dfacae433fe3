import React, { useState, useEffect } from 'react';
import { generateDailyPrompt, suggestContentFormat } from '../services/aiService';
import { useTranslation } from '../hooks/useTranslation';
import { toast } from '../utils/toast';

interface CreatorDailyProps {
  isDragging: boolean;
  onRemove: () => void;
}

interface DailyContent {
  prompt: string;
  format: {
    type: string;
    direction: string;
    hook: string;
  } | null;
  generatedContent: string | null;
  lastGenerated: string; // Date string
}

const CreatorDaily: React.FC<CreatorDailyProps> = ({ isDragging, onRemove }) => {
  const { t } = useTranslation();
  const [dailyContent, setDailyContent] = useState<DailyContent | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editedContent, setEditedContent] = useState('');

  // Load or generate daily content
  useEffect(() => {
    const loadDailyContent = async () => {
      setIsLoading(true);
      try {
        // Check if we have today's content in storage
        const today = new Date().toDateString();
        const result = await chrome.storage.local.get(['creatorDaily']);
        const stored = result.creatorDaily;

        if (stored && stored.lastGenerated === today) {
          // Use stored content from today
          setDailyContent(stored);
        } else {
          // Generate new content for today
          await generateNewDailyContent();
        }
      } catch (error) {
        console.error('Error loading daily content:', error);
        // Fallback to default content
        setDailyContent({
          prompt: "Share a moment when you learned something unexpected. What was it, and how did it change your perspective?",
          format: {
            type: "Tweet/X Post",
            direction: "Frame as a personal story that invites others to share their own experiences.",
            hook: "Today I learned something that completely shifted how I think about..."
          },
          generatedContent: null,
          lastGenerated: new Date().toDateString()
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadDailyContent();
  }, []);

  // Generate new daily content
  const generateNewDailyContent = async () => {
    try {
      const prompt = await generateDailyPrompt();
      const format = await suggestContentFormat(prompt);
      
      const newContent: DailyContent = {
        prompt,
        format,
        generatedContent: null,
        lastGenerated: new Date().toDateString()
      };

      setDailyContent(newContent);
      
      // Save to storage
      await chrome.storage.local.set({ creatorDaily: newContent });
      
    } catch (error) {
      console.error('Error generating daily content:', error);
      throw error;
    }
  };

  // Generate content based on the prompt
  const handleGenerate = async () => {
    if (!dailyContent?.format) return;

    setIsGenerating(true);
    try {
      // For now, use the hook as generated content
      // In a real implementation, this could call another AI service
      const generated = dailyContent.format.hook;
      
      const updatedContent = {
        ...dailyContent,
        generatedContent: generated
      };
      
      setDailyContent(updatedContent);
      setEditedContent(generated);
      
      // Save to storage
      await chrome.storage.local.set({ creatorDaily: updatedContent });
      
      toast.success('Content generated successfully!');
    } catch (error) {
      console.error('Error generating content:', error);
      toast.error('Failed to generate content. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle edit mode
  const handleEdit = () => {
    setIsEditMode(true);
    setEditedContent(dailyContent?.generatedContent || dailyContent?.format?.hook || '');
  };

  // Save edited content
  const handleSaveEdit = async () => {
    if (!dailyContent) return;

    const updatedContent = {
      ...dailyContent,
      generatedContent: editedContent
    };

    setDailyContent(updatedContent);
    setIsEditMode(false);
    
    // Save to storage
    await chrome.storage.local.set({ creatorDaily: updatedContent });
    
    toast.success('Content updated successfully!');
  };

  // Cancel edit
  const handleCancelEdit = () => {
    setIsEditMode(false);
    setEditedContent(dailyContent?.generatedContent || '');
  };

  // Add to weekly plan (placeholder)
  const handleAddToWeeklyPlan = () => {
    if (!dailyContent?.generatedContent && !dailyContent?.format?.hook) {
      toast.error('Generate content first to add to weekly plan.');
      return;
    }

    // Copy to clipboard for now
    const content = dailyContent.generatedContent || dailyContent.format?.hook || '';
    navigator.clipboard.writeText(content).then(() => {
      toast.success('Content copied to clipboard! Weekly planning feature coming soon.');
    }).catch(() => {
      toast.error('Failed to copy content');
    });
  };

  // Get format icon
  const getFormatIcon = (format: string): string => {
    switch (format) {
      case 'Tweet/X Post': return '🐦';
      case 'Blog Intro': return '📝';
      case 'Video Hook': return '🎬';
      default: return '✨';
    }
  };

  // Get format color
  const getFormatColor = (format: string): string => {
    switch (format) {
      case 'Tweet/X Post': return 'bg-blue-500/10 text-blue-400 border-blue-500/20';
      case 'Blog Intro': return 'bg-green-500/10 text-green-400 border-green-500/20';
      case 'Video Hook': return 'bg-purple-500/10 text-purple-400 border-purple-500/20';
      default: return 'bg-notely-accent/10 text-notely-accent border-notely-accent/20';
    }
  };

  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center text-center py-8">
        <div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-notely-accent mx-auto mb-2"></div>
          <p className="text-sm text-notely-text-secondary">Loading daily inspiration...</p>
          <p className="text-xs text-notely-text-tertiary mt-1">Generating your creative prompt</p>
        </div>
      </div>
    );
  }

  if (!dailyContent) {
    return (
      <div className="text-center py-8">
        <span className="text-4xl mb-2 block">✨</span>
        <p className="text-sm text-notely-text-secondary mb-2">Unable to load daily prompt</p>
        <p className="text-xs text-notely-text-tertiary">Please try refreshing the page</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-notely-accent to-notely-accent-secondary rounded-notely-md flex items-center justify-center">
            <span className="text-white text-lg">✨</span>
          </div>
          <div>
            <h4 className="text-xs font-semibold text-notely-text-secondary mb-1 uppercase tracking-wide">
              Daily Inspiration
            </h4>
            <p className="text-xs text-notely-text-tertiary">
              Your creative prompt for today
            </p>
          </div>
        </div>

        {/* Format Badge */}
        {dailyContent.format && (
          <div className={`flex items-center space-x-1 px-2 py-1 rounded-notely-full border text-xs font-medium ${getFormatColor(dailyContent.format.type)}`}>
            <span>{getFormatIcon(dailyContent.format.type)}</span>
            <span>{dailyContent.format.type}</span>
          </div>
        )}
      </div>

      {/* Prompt */}
      <div className="bg-notely-surface rounded-notely-md p-4 border border-notely-border">
        <p className="text-sm text-notely-text-primary leading-relaxed font-medium mb-2">
          {dailyContent.prompt}
        </p>
        {dailyContent.format && (
          <p className="text-xs text-notely-text-secondary italic">
            💡 {dailyContent.format.direction}
          </p>
        )}
      </div>

      {/* Generated Content or Edit Mode */}
      {isEditMode ? (
        <div className="space-y-3">
          <textarea
            value={editedContent}
            onChange={(e) => setEditedContent(e.target.value)}
            className="w-full p-3 bg-notely-bg border border-notely-border rounded-notely-md text-sm text-notely-text-primary placeholder-notely-text-tertiary resize-none focus:outline-none focus:ring-2 focus:ring-notely-accent"
            rows={3}
            placeholder="Edit your content..."
          />
          <div className="flex items-center space-x-2">
            <button
              onClick={handleSaveEdit}
              className="notely-btn-primary text-xs px-3 py-1.5 notely-filter-transition hover:scale-105"
            >
              Save
            </button>
            <button
              onClick={handleCancelEdit}
              className="notely-btn-secondary text-xs px-3 py-1.5 notely-filter-transition hover:scale-105"
            >
              Cancel
            </button>
          </div>
        </div>
      ) : dailyContent.generatedContent && (
        <div className="p-3 bg-notely-bg rounded-notely-md border border-notely-border">
          <p className="text-sm text-notely-text-primary">
            {dailyContent.generatedContent}
          </p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={handleGenerate}
          disabled={isGenerating}
          className="notely-btn-primary text-xs px-3 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed notely-filter-transition hover:scale-105"
        >
          {isGenerating ? (
            <span className="flex items-center space-x-1">
              <div className="animate-spin rounded-full h-3 w-3 border-b border-white"></div>
              <span>Generating...</span>
            </span>
          ) : dailyContent.generatedContent ? 'Regenerate' : 'Generate'}
        </button>

        {dailyContent.generatedContent && !isEditMode && (
          <button
            onClick={handleEdit}
            className="notely-btn-secondary text-xs px-3 py-1.5 notely-filter-transition hover:scale-105"
          >
            Edit
          </button>
        )}

        {(dailyContent.generatedContent || dailyContent.format?.hook) && (
          <button
            onClick={handleAddToWeeklyPlan}
            className="notely-btn-secondary text-xs px-3 py-1.5 notely-filter-transition hover:scale-105"
          >
            Add to Weekly Plan
          </button>
        )}
      </div>
    </div>
  );
};

export default CreatorDaily;
