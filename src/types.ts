// Type definitions placeholder

export type Platform = 'X/Twitter' | 'LinkedIn' | 'Reddit' | 'Instagram' | 'facebook' | 'pinterest' | 'Web';

export type Category = string; // Or define specific categories

import { CoreSubCategorySlug } from './config/constants';
import type { InSightData } from './services/aiService';

export interface MediaItem {
  type: 'image' | 'video';
  url: string;
  alt?: string;
  width?: number;
  height?: number;
  filePath?: string; // Path in Firebase storage, if applicable
  size?: number; // Size of the media file in bytes
}

export interface Post {
  id: string; // Unique ID for the post (e.g., tweet ID, Reddit post ID)
  _id?: string | object; // MongoDB _id field when coming from the backend
  originalPostId?: string; // Original platform ID stored in MongoDB
  platform: Platform;
  author: string; // Simplified author string for direct display
  authorName?: string;
  authorHandle?: string;
  authorUrl?: string; // URL to the author's profile page
  authorAvatar?: string; // URL to the author's avatar
  authorImage?: string; // Fallback or alternative image field
  content?: string; // Text content of the post
  textContent?: string; // Added for Twitter/X content extraction
  title?: string; // Title of the post, if applicable (e.g., for articles)
  altText?: string; // Alt text for media
  timestamp?: string; // Original post timestamp (ISO format)
  createdAt?: string; // Timestamp when the post was created (either original or when first seen by system)
  savedAt: string; // Timestamp when the post was saved (ISO format)
  permalink: string; // URL to the original post
  media?: MediaItem[]; // Array of media items (images, videos)
  stats?: { // Changed from interactions to stats
    comments?: number; // Changed from replies to comments for broader compatibility
    shares?: number;   // Changed from reposts to shares
    likes?: number;
    views?: number; // Added for consistency, though not yet fully plumbed
  };
  categories?: string[]; // User-assigned categories (up to 3)
  tags?: string[]; // User-assigned tags (up to 5)
  savedImage?: string; // Base64 data URL if image was saved
  notes?: string; // User-added notes
  isThread?: boolean; // True if this post is part of a thread
  threadId?: string; // Unique ID for the thread this post belongs to
  threadParentId?: string; // ID of the parent post in the thread (if not the first post)
  threadPosition?: number; // Position of this post in the thread (1-based)
  threadLength?: number; // Total number of posts in the thread
  isProcessed?: boolean; // Flag for tracking processing status
  uploadStatus?: 'pending' | 'uploading' | 'uploaded' | 'failed' | 'local-only'; // Cloud upload status
  source?: 'local' | 'cloud' | 'synced'; // Source of the post data
}

export interface AnalyzedPost extends Post {
  // author: string; // Inherited from Post
  snapNote?: string | null;         // Generated by GPT-4o, especially for images
  categories: CoreSubCategorySlug[]; // Final categories from categorizer.ts (overrides Post.categories)
  tags: string[];                    // Final tags from categorizer.ts (consistent with Post.tags, but finalized)
  embeddingVector?: number[];       // Embedding vector from analysisService.ts
  analyzedAt?: string;              // ISO timestamp of when AI analysis was performed
  inSight?: InSightData | null;       // <-- NEW FIELD
  fastTake?: string | null;         // <-- NEW FIELD
  contentIdeas?: string[] | null;   // <-- NEW FIELD
}

// Interface for user data fetched from the backend (/auth/me)
export interface IUserFrontend {
  id: string; // MongoDB _id
  name: string;
  displayName?: string;
  email: string;
  googleId?: string;
  plan: 'free' | 'premium';
  createdAt: string; // Assuming ISO string from backend
  lastLogin: string;  // Assuming ISO string from backend
  profileSettings?: Record<string, unknown>;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
}

// --- Chrome Extension Messaging Interfaces ---
export interface ChromeMessage {
  action: string;
  data?: unknown; // Changed from any to unknown
  postData?: Post | AnalyzedPost; // For SAVE_POST_REQUEST
  postId?: string; // For DELETE_POST_REQUEST
  category?: string; // For actions related to categories
  tag?: string; // For actions related to tags
  imageUrl?: string; // For FETCH_IMAGE_AS_DATA_URL
}

export interface ChromeResponse {
  status: 'success' | 'error' | 'duplicate' | 'unauthorized' | 'not_found' | 'pending';
  message?: string;
  data?: unknown; // Changed from any to unknown // General data payload
  postId?: string; // Include postId in responses where relevant
  token?: string | null; // For GET_AUTH_TOKEN response
  dataUrl?: string; // For FETCH_IMAGE_AS_DATA_URL response
}

export type { CoreSubCategorySlug, InSightData }; // Re-export if needed by other modules importing from types.ts

// AI Interaction Types
export interface AIResponse {
  status: string; // Placeholder to avoid empty interface error
  data?: unknown;
  error?: string;
}

// Category Overview Types
export interface CategoryOverview {
  summary: string;
  essence: string;
  keyInsights: string[];
  thematicTags: string[];
  patterns: string[];
  userMotivations: string[];
  postCount: number;
  categoryName: string;
  generatedAt: string;
  isLoading?: boolean;
  error?: string;
}

// Content-based categorization types
export interface ContentCategory {
  posts: Post[];
  summary: string;
  essence: string;
  keyInsights: string[];
  thematicTags: string[];
  patterns: string[];
}

export interface ContentCategorization {
  categories: Record<string, ContentCategory>;
  uncategorized: Post[];
}
