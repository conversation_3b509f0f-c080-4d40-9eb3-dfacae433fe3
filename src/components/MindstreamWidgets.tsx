import React, { useState, useEffect } from 'react';
import { Post } from '../types';
import { DraggableItem } from '../hooks/useDragAndDrop';

import SavedBookmarksWidget from './SavedBookmarksWidget';
import CategoryTagManager from './CategoryTagManager';
import UseWhatYouSavedWidget from './UseWhatYouSavedWidget';
import CreatorDaily from './CreatorDaily';
import SmartRecap from './SmartRecap';
import ContentCreationToolbox from './ContentCreationToolbox';
import InspireToDoWidget from './InspireToDoWidget';
import { useTranslation } from '../hooks/useTranslation';

interface MindstreamWidgetsProps {
  posts: Post[];
  className?: string;
}

const MindstreamWidgets: React.FC<MindstreamWidgetsProps> = ({ posts, className = '' }) => {
  console.log('[MindstreamWidgets] Received posts prop (first 3):', JSON.stringify(posts.slice(0, 3), null, 2));
  console.log('[MindstreamWidgets] Component rendered with', posts.length, 'posts');
  const { t } = useTranslation();
  const [widgets, setWidgets] = useState<DraggableItem[]>([]);

  const removeItem = (id: string) => {
    setWidgets(prev => prev.filter(item => item.id !== id));
  };

  // Initialize default widgets
  useEffect(() => {
    const defaultWidgets: DraggableItem[] = [
      {
        id: 'creator-daily',
        type: 'creator-daily',
        position: { x: 0, y: 0 },
        size: { width: 2, height: 1 },
        data: { span: 2 }
      },
      {
        id: 'inspire-to-do',
        type: 'inspire-to-do',
        position: { x: 0, y: 1 },
        size: { width: 1, height: 1 },
        data: { span: 1 }
      },
      {
        id: 'content-creation-toolbox',
        type: 'content-creation-toolbox',
        position: { x: 1, y: 1 },
        size: { width: 1, height: 1 },
        data: { span: 1 }
      },
      {
        id: 'smart-recap',
        type: 'smart-recap',
        position: { x: 0, y: 2 },
        size: { width: 2, height: 1 },
        data: { span: 2 }
      },
      {
        id: 'category-tag-manager',
        type: 'category-tag-manager',
        position: { x: 0, y: 3 },
        size: { width: 2, height: 1 },
        data: { span: 2 }
      },
      {
        id: 'bookmark-grid',
        type: 'bookmark-grid',
        position: { x: 0, y: 4 },
        size: { width: 2, height: 1 },
        data: { span: 2 }
      },
      {
        id: 'use-what-you-saved',
        type: 'use-what-you-saved',
        position: { x: 0, y: 5 },
        size: { width: 2, height: 1 },
        data: { span: 2 }
      }
    ];
    setWidgets(defaultWidgets);
  }, []);

  // Get posts with links for bookmark grid
  // const getBookmarkPosts = (allPosts: Post[]): Post[] => allPosts
  //     .filter(post => post.content && (post.content.includes('http') || post.permalink))
  //     .slice(0, 4);

  // Simple reorder functionality
  const moveWidgetUp = (itemId: string) => {
    const currentIndex = widgets.findIndex(w => w.id === itemId);
    if (currentIndex > 0) {
      const newWidgets = [...widgets];
      [newWidgets[currentIndex - 1], newWidgets[currentIndex]] = [newWidgets[currentIndex], newWidgets[currentIndex - 1]];
      setWidgets(newWidgets);
      console.log('[MindstreamWidgets] Moved widget up:', itemId);
    }
  };

  const moveWidgetDown = (itemId: string) => {
    const currentIndex = widgets.findIndex(w => w.id === itemId);
    if (currentIndex < widgets.length - 1) {
      const newWidgets = [...widgets];
      [newWidgets[currentIndex], newWidgets[currentIndex + 1]] = [newWidgets[currentIndex + 1], newWidgets[currentIndex]];
      setWidgets(newWidgets);
      console.log('[MindstreamWidgets] Moved widget down:', itemId);
    }
  };

  // Common wrapper for all widgets with reorder functionality
  const WidgetWrapper = ({ children, title, item }: { children: React.ReactNode; title: string; item: DraggableItem }) => {
    const currentIndex = widgets.findIndex(w => w.id === item.id);
    const canMoveUp = currentIndex > 0;
    const canMoveDown = currentIndex < widgets.length - 1;

    return (
      <div
        className={`
          group relative bg-notely-card rounded-notely-lg border border-notely-border
          hover:border-notely-accent/30 hover:shadow-notely-lg hover:-translate-y-1
          transition-all duration-200
        `}
      >
        {/* Reorder Controls - Always visible and intuitive */}
        <div className="absolute top-3 right-3 z-20 flex flex-col space-y-1 opacity-60 group-hover:opacity-100 transition-opacity">
          <button
            onClick={() => moveWidgetUp(item.id)}
            disabled={!canMoveUp}
            className={`
              flex items-center justify-center w-7 h-7 rounded-notely-md transition-all duration-200
              ${canMoveUp
                ? 'bg-notely-accent text-white hover:bg-notely-accent-secondary hover:scale-110 shadow-md hover:shadow-lg'
                : 'bg-notely-surface text-notely-text-tertiary cursor-not-allowed opacity-40'
              }
            `}
            title={canMoveUp ? `Move "${title}" up in the layout` : 'Already at the top'}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2.5}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M5 15l7-7 7 7" />
            </svg>
          </button>

          <button
            onClick={() => moveWidgetDown(item.id)}
            disabled={!canMoveDown}
            className={`
              flex items-center justify-center w-7 h-7 rounded-notely-md transition-all duration-200
              ${canMoveDown
                ? 'bg-notely-accent text-white hover:bg-notely-accent-secondary hover:scale-110 shadow-md hover:shadow-lg'
                : 'bg-notely-surface text-notely-text-tertiary cursor-not-allowed opacity-40'
              }
            `}
            title={canMoveDown ? `Move "${title}" down in the layout` : 'Already at the bottom'}
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2.5}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>

        {/* Widget Header */}
        <div className="flex items-center justify-between p-4 pb-2">
          <h3 className="text-lg font-semibold text-notely-text-primary notely-heading">{title}</h3>
        </div>

        {/* Widget Content */}
        <div className="p-4 pt-0">
          {children}
        </div>
      </div>
    );
  };

  const renderWidget = (item: DraggableItem, index: number) => {
    console.log('[MindstreamWidgets] Rendering widget:', item.type, 'with id:', item.id);

    switch (item.type) {
      case 'creator-daily':
        return (
          <WidgetWrapper title={t('widgets.creatorDaily', 'Creator Daily')} item={item}>
            <CreatorDaily
              isDragging={false}
              onRemove={() => removeItem(item.id)}
            />
          </WidgetWrapper>
        );

      case 'inspire-to-do':
        return (
          <WidgetWrapper title={t('widgets.inspireToDo', 'Inspire-To-Do')} item={item}>
            <InspireToDoWidget
              posts={posts}
              isDragging={false}
              onRemove={() => removeItem(item.id)}
            />
          </WidgetWrapper>
        );

      case 'content-creation-toolbox':
        return (
          <WidgetWrapper title={t('widgets.contentCreationToolbox', 'Content Creation Toolbox')} item={item}>
            <ContentCreationToolbox
              isDragging={false}
              onRemove={() => removeItem(item.id)}
            />
          </WidgetWrapper>
        );

      case 'smart-recap':
        return (
          <WidgetWrapper title={t('widgets.smartRecap', 'Smart Recap')} item={item}>
            <SmartRecap
              posts={posts}
              className="w-full"
            />
          </WidgetWrapper>
        );

      case 'category-tag-manager':
        return (
          <WidgetWrapper title={t('widgets.categoryTagManager', 'Category & Tag Manager')} item={item}>
            <CategoryTagManager
              posts={posts}
              isDragging={false}
              onRemove={() => removeItem(item.id)}
            />
          </WidgetWrapper>
        );



      case 'bookmark-grid':
        return (
          <WidgetWrapper title={t('widgets.bookmarks', 'Saved Bookmarks')} item={item}>
            <SavedBookmarksWidget
              posts={posts}
              isDragging={false}
              onRemove={() => removeItem(item.id)}
            />
          </WidgetWrapper>
        );

      case 'use-what-you-saved':
        return (
          <WidgetWrapper title={t('widgets.useWhatYouSaved', 'Use What You Saved')} item={item}>
            <UseWhatYouSavedWidget
              posts={posts}
              isDragging={false}
              onRemove={() => removeItem(item.id)}
            />
          </WidgetWrapper>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Instructions for users */}
      <div className="bg-gradient-to-r from-notely-accent/10 to-notely-accent-secondary/10 rounded-notely-lg p-4 border border-notely-accent/20">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-notely-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
            </svg>
          </div>
          <div>
            <p className="text-sm font-medium text-notely-text-primary">
              {t('widgets.customizeLayout', 'Customize Your Layout')}
            </p>
            <p className="text-xs text-notely-text-secondary mt-1">
              {t('widgets.clickToReorder', 'Use the arrow buttons (↑↓) to reorder widgets')}
            </p>
          </div>
        </div>
      </div>

      {/* Clean 2-column grid layout */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {widgets.map((item, index) => renderWidget(item, index))}
      </div>
    </div>
  );
};

export default MindstreamWidgets;
