import React, { useState } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { toast } from '../utils/toast';

interface ContentCreationToolboxProps {
  isDragging: boolean;
  onRemove: () => void;
}

interface Tool {
  id: string;
  name: string;
  description: string;
  icon: string;
  shortcut?: string;
  color: string;
  bgColor: string;
  hoverColor: string;
  comingSoon?: boolean;
}

const ContentCreationToolbox: React.FC<ContentCreationToolboxProps> = ({ isDragging, onRemove }) => {
  const { t } = useTranslation();
  const [hoveredTool, setHoveredTool] = useState<string | null>(null);

  const tools: Tool[] = [
    {
      id: 'hook-generator',
      name: 'Hook Generator',
      description: 'Create compelling opening lines that grab attention instantly',
      icon: '🎣',
      shortcut: 'H',
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
      hoverColor: 'hover:bg-blue-500/20'
    },
    {
      id: 'thread-composer',
      name: 'Thread Composer',
      description: 'Build engaging Twitter/X threads with perfect flow and structure',
      icon: '🧵',
      shortcut: 'T',
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/10',
      hoverColor: 'hover:bg-purple-500/20'
    },
    {
      id: 'reels-caption',
      name: 'Reels Caption Writer',
      description: 'Generate catchy captions for Instagram Reels and TikTok videos',
      icon: '🎬',
      shortcut: 'R',
      color: 'text-pink-400',
      bgColor: 'bg-pink-500/10',
      hoverColor: 'hover:bg-pink-500/20'
    },
    {
      id: 'format-converter',
      name: 'Format Converter',
      description: 'Transform content between formats: tweet → blog → video script',
      icon: '🔄',
      shortcut: 'F',
      color: 'text-green-400',
      bgColor: 'bg-green-500/10',
      hoverColor: 'hover:bg-green-500/20'
    },
    {
      id: 'quote-visualizer',
      name: 'Quote Visualizer',
      description: 'Turn powerful quotes into beautiful visual graphics',
      icon: '🎨',
      shortcut: 'Q',
      color: 'text-orange-400',
      bgColor: 'bg-orange-500/10',
      hoverColor: 'hover:bg-orange-500/20'
    },
    {
      id: 'content-optimizer',
      name: 'Content Optimizer',
      description: 'Enhance your content for better engagement and reach',
      icon: '⚡',
      shortcut: 'O',
      color: 'text-yellow-400',
      bgColor: 'bg-yellow-500/10',
      hoverColor: 'hover:bg-yellow-500/20',
      comingSoon: true
    }
  ];

  const handleToolClick = (tool: Tool) => {
    if (tool.comingSoon) {
      // Show coming soon toast
      toast.info(`${tool.name} is coming soon! 🚀`);
      return;
    }

    // Handle tool activation
    console.log(`Activating ${tool.name}`);

    // Show activation feedback
    toast.success(`${tool.name} activated! ✨`);

    // TODO: Implement tool-specific functionality
    switch (tool.id) {
      case 'hook-generator':
        // Open hook generator modal/panel
        break;
      case 'thread-composer':
        // Open thread composer
        break;
      case 'reels-caption':
        // Open reels caption writer
        break;
      case 'format-converter':
        // Open format converter
        break;
      case 'quote-visualizer':
        // Open quote visualizer
        break;
      default:
        break;
    }
  };

  const handleKeyboardShortcut = (event: React.KeyboardEvent) => {
    if (event.ctrlKey || event.metaKey) {
      const key = event.key.toLowerCase();
      const tool = tools.find(t => t.shortcut?.toLowerCase() === key);
      if (tool) {
        event.preventDefault();
        handleToolClick(tool);
      }
    }
  };

  return (
    <div 
      className="space-y-4"
      onKeyDown={handleKeyboardShortcut}
      tabIndex={0}
    >
      {/* Header with description */}
      <div className="text-center">
        <p className="text-xs text-notely-text-secondary mb-4">
          Your creative toolkit for content generation and optimization
        </p>
      </div>

      {/* Tools Grid */}
      <div className="grid grid-cols-2 gap-3">
        {tools.map((tool) => (
          <div
            key={tool.id}
            className={`
              relative group cursor-pointer rounded-notely-lg border border-notely-border
              ${tool.bgColor} ${tool.hoverColor}
              transition-all duration-300 ease-out
              hover:border-notely-accent/30 hover:shadow-lg hover:-translate-y-1
              ${tool.comingSoon ? 'opacity-60 cursor-not-allowed' : 'hover:scale-[1.02]'}
              ${hoveredTool === tool.id && !tool.comingSoon ? 'scale-[1.02] shadow-md' : ''}
              focus:outline-none focus:ring-2 focus:ring-notely-accent/50 focus:ring-offset-2 focus:ring-offset-notely-bg
            `}
            onClick={() => handleToolClick(tool)}
            onMouseEnter={() => setHoveredTool(tool.id)}
            onMouseLeave={() => setHoveredTool(null)}
            tabIndex={0}
            role="button"
            aria-label={`${tool.name}: ${tool.description}`}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleToolClick(tool);
              }
            }}
          >
            {/* Coming Soon Badge */}
            {tool.comingSoon && (
              <div className="absolute -top-2 -right-2 z-10">
                <span className="bg-notely-accent text-white text-xs px-2 py-1 rounded-notely-full font-medium">
                  Soon
                </span>
              </div>
            )}

            {/* Keyboard Shortcut */}
            {tool.shortcut && (
              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <span className="bg-notely-surface text-notely-text-tertiary text-xs px-1.5 py-0.5 rounded border border-notely-border font-mono">
                  ⌘{tool.shortcut}
                </span>
              </div>
            )}

            <div className="p-4">
              {/* Icon */}
              <div className="flex items-center justify-center mb-3">
                <div className={`
                  w-12 h-12 rounded-notely-lg flex items-center justify-center
                  bg-notely-surface border border-notely-border
                  group-hover:scale-110 transition-transform duration-200
                `}>
                  <span className="text-2xl">{tool.icon}</span>
                </div>
              </div>

              {/* Content */}
              <div className="text-center">
                <h4 className="text-sm font-semibold text-notely-text-primary mb-1 notely-heading">
                  {tool.name}
                </h4>
                <p className="text-xs text-notely-text-secondary leading-relaxed">
                  {tool.description}
                </p>
              </div>

              {/* Hover Effect Overlay */}
              <div className={`
                absolute inset-0 rounded-notely-lg opacity-0 group-hover:opacity-100
                transition-opacity duration-300 pointer-events-none
                bg-gradient-to-br from-transparent via-transparent to-notely-accent/5
              `} />

              {/* Active State Indicator */}
              {hoveredTool === tool.id && !tool.comingSoon && (
                <div className="absolute bottom-2 left-2 right-2">
                  <div className="h-0.5 bg-gradient-to-r from-transparent via-notely-accent to-transparent rounded-full opacity-60" />
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Footer Tips */}
      <div className="mt-6 p-3 bg-notely-surface rounded-notely-md border border-notely-border">
        <div className="flex items-center space-x-2 text-xs text-notely-text-tertiary">
          <span className="w-4 h-4 bg-notely-accent/10 rounded flex items-center justify-center">
            💡
          </span>
          <span>
            Use <kbd className="px-1 py-0.5 bg-notely-bg rounded border border-notely-border font-mono">⌘ + Key</kbd> for quick access
          </span>
        </div>
      </div>
    </div>
  );
};

export default ContentCreationToolbox;
