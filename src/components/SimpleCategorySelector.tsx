import React from 'react';
import { formatForDisplay } from '../utils/formatUtils';

interface SimpleCategorySelectorProps {
  categories: string[];
  selectedCategory: string | null;
  onCategorySelect: (category: string | null) => void;
  className?: string;
  categoryCounts?: Record<string, number>;
}

const SimpleCategorySelector: React.FC<SimpleCategorySelectorProps> = ({
  categories,
  selectedCategory,
  onCategorySelect,
  className = '',
  categoryCounts = {}
}) => {
  if (categories.length === 0) {
    return null;
  }

  const getButtonStyles = (isSelected: boolean) => {
    const baseStyles = 'px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 focus:outline-none relative transform-gpu will-change-transform active:scale-95';

    if (isSelected) {
      return `${baseStyles} bg-notely-mint text-white shadow-notely-lg font-semibold border-transparent hover:bg-notely-mint/90 hover:scale-105 hover:shadow-notely-xl after:content-['✓'] after:absolute after:top-1 after:right-1 after:text-xs after:text-white/80 after:transition-all after:duration-200`;
    }

    return `${baseStyles} bg-notely-surface text-notely-text-secondary border border-notely-border hover:bg-notely-card hover:text-notely-text-primary hover:shadow-notely-md hover:border-notely-mint/30 hover:scale-105 hover:-translate-y-0.5`;
  };

  return (
    <div className={`flex flex-wrap gap-2 py-3 ${className}`}>
      {categories.map((category, index) => (
        <button
          key={category}
          onClick={() => onCategorySelect(selectedCategory === category ? null : category)}
          className={`${getButtonStyles(selectedCategory === category)} notely-category-button`}
          style={{ animationDelay: `${index * 0.05}s` }}
        >
          {formatForDisplay(category)}
          {categoryCounts[category] > 0 && (
            <span className="ml-2 text-xs bg-notely-surface text-notely-text-secondary rounded-full px-2 py-0.5">
              {categoryCounts[category]}
            </span>
          )}
        </button>
      ))}
    </div>
  );
};

export default SimpleCategorySelector;
