import React, { useState, useRef, useEffect, KeyboardEvent } from 'react';
import { useTranslation } from '../hooks/useTranslation';

interface MultiItemInputProps {
  label: string;
  items: string[]; // Currently selected items
  allItems: string[]; // Master list for suggestions
  maxItems: number;
  placeholder: string;
  onChange: (newItems: string[]) => void;
}

const MultiItemInput: React.FC<MultiItemInputProps> = ({
  label,
  items,
  allItems,
  maxItems,
  placeholder,
  onChange,
}) => {
  const { t } = useTranslation();
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLUListElement>(null);

  // Filter suggestions based on input (excluding already selected items)
  useEffect(() => {
    if (inputValue.trim() === '') {
      setSuggestions([]);
      setShowDropdown(false);
      return;
    }
    const lowerInput = inputValue.toLowerCase();
    const filtered = allItems
      .filter(item =>
          item.toLowerCase().includes(lowerInput) &&
          !items.some(sel => sel.toLowerCase() === item.toLowerCase())
      )
      .slice(0, 5); // Limit suggestions
    setSuggestions(filtered);
    setShowDropdown(true);
  }, [inputValue, allItems, items]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          inputRef.current && !inputRef.current.contains(event.target as Node)) {
        setShowDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
    setShowDropdown(true);
  };

  const handleInputFocus = () => {
    if (inputValue.trim()) {
      setShowDropdown(true);
    }
  };

  const handleAddItem = (itemToAdd: string) => {
    const trimmedItem = itemToAdd.trim();
    if (
      trimmedItem &&
      items.length < maxItems &&
      !items.some(item => item.toLowerCase() === trimmedItem.toLowerCase())
    ) {
      const newItems = [...items, trimmedItem];
      onChange(newItems);
      setInputValue('');
      setSuggestions([]);
      setShowDropdown(false);
    }
  };

  const handleRemoveItem = (itemToRemove: string) => {
    const newItems = items.filter(item => item !== itemToRemove);
    onChange(newItems);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault(); // Prevent form submission or comma typing
      if (inputValue.trim()) {
        handleAddItem(inputValue);
      }
    }
    if (e.key === 'Backspace' && inputValue === '' && items.length > 0) {
      // Remove last item on backspace if input is empty
      handleRemoveItem(items[items.length - 1]);
    }
    if (e.key === 'Escape') {
      setShowDropdown(false);
    }
  };

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-gray-700 mb-1">{label}</label>
      <div className="flex flex-wrap items-center gap-2 p-2 border border-gray-300 rounded-md bg-white min-h-[40px]">
        {items.map(item => (
          <span key={item} className="flex items-center px-2 py-0.5 bg-gray-200 text-gray-800 text-sm font-medium rounded-full">
            {item}
            <button
              type="button"
              onClick={() => handleRemoveItem(item)}
              className="ml-1.5 text-gray-500 hover:text-red-600 focus:outline-none"
              aria-label={t('multiItemInput.remove').replace('{item}', item)}
            >
              ×
            </button>
          </span>
        ))}
        {items.length < maxItems && (
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={handleInputFocus}
            placeholder={items.length === 0 ? placeholder : ''}
            className="flex-grow p-1 outline-none text-sm min-w-[120px]"
            disabled={items.length >= maxItems}
          />
        )}
      </div>

      {/* Suggestions Dropdown */}
      {showDropdown && items.length < maxItems && (inputValue.trim() || suggestions.length > 0) && (
        <ul
          ref={dropdownRef}
          className="absolute left-0 right-0 mt-1 border border-gray-300 rounded-md bg-white shadow-sm max-h-40 overflow-y-auto z-10"
        >
          {suggestions.map(suggestion => (
            <li
              key={suggestion}
              onClick={() => handleAddItem(suggestion)}
              className="px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
            >
              {suggestion}
            </li>
          ))}
          {/* Always show "Add new" option if input has value and it's not in suggestions or selected items */}
          {inputValue.trim() &&
           !suggestions.some(s => s.toLowerCase() === inputValue.trim().toLowerCase()) &&
           !items.some(i => i.toLowerCase() === inputValue.trim().toLowerCase()) && (
            <li
              onClick={() => handleAddItem(inputValue)}
              className="px-3 py-2 text-sm text-blue-600 hover:bg-gray-100 cursor-pointer border-t border-gray-100"
            >
              {t('multiItemInput.add').replace('{item}', inputValue.trim())}
            </li>
          )}
        </ul>
      )}
      {items.length >= maxItems && (
        <p className="text-xs text-red-500 mt-1">{t('multiItemInput.maxItemsReached')}</p>
      )}
    </div>
  );
};

export default MultiItemInput;