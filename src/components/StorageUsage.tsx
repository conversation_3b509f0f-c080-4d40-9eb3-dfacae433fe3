import React, { useState, useEffect } from 'react';

// API URL for backend
const API_URL = 'https://api.notely.social';

// Storage usage types
interface StorageUsageType {
  usedMB: number;
  limitMB: number;
  usagePercentage: number;
  plan: 'free' | 'premium';
  isNearLimit: boolean;
  isOverLimit: boolean;
}

// Get authentication token from Chrome storage
const getAuthToken = async (): Promise<string | null> => {
  try {
    const result = await chrome.storage.local.get(['token', 'authToken']);
    return result.authToken || result.token || null;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
};

// Fetch real storage usage from backend API
const getStorageUsage = async (): Promise<StorageUsageType> => {
  try {
    const token = await getAuthToken();

    if (!token) {
      throw new Error('No authentication token available');
    }

    const response = await fetch(`${API_URL}/auth/storage-usage`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      if (response.status === 401) {
        throw new Error('Authentication token expired');
      }
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    return {
      usedMB: data.usedMB,
      limitMB: data.limitMB,
      usagePercentage: data.usagePercentage,
      plan: data.plan,
      isNearLimit: data.isNearLimit,
      isOverLimit: data.isOverLimit
    };
  } catch (error) {
    console.error('Error fetching storage usage:', error);
    throw error;
  }
};

const getStorageUsageColor = (percentage: number): string => {
  if (percentage >= 90) return '#ef4444'; // red
  if (percentage >= 75) return '#f59e0b'; // yellow
  return '#10b981'; // green
};

const getStorageUsageMessage = (data: StorageUsageType): string => {
  if (data.isOverLimit) return 'Storage limit exceeded. Please delete some posts or upgrade to Premium.';
  if (data.isNearLimit) return 'Storage is almost full. Consider deleting old posts.';
  return 'Storage usage is healthy.';
};

const getRecommendedAction = (data: StorageUsageType): string | null => {
  if (data.isOverLimit) return 'Delete old posts or upgrade to Premium for more storage.';
  if (data.isNearLimit) return 'Consider cleaning up old posts to free up space.';
  return null;
};

interface StorageUsageProps {
  className?: string;
  showDetails?: boolean;
}

const StorageUsage: React.FC<StorageUsageProps> = ({
  className = '',
  showDetails = true
}) => {
  const [storageData, setStorageData] = useState<StorageUsageType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  // Check authentication status
  const checkAuthStatus = async () => {
    const token = await getAuthToken();
    setIsAuthenticated(!!token);
    return !!token;
  };

  useEffect(() => {
    const initializeComponent = async () => {
      const isAuth = await checkAuthStatus();
      if (isAuth) {
        await fetchStorageUsage();
      } else {
        setLoading(false);
      }
    };

    initializeComponent();

    // Listen for authentication changes and post updates
    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }) => {
      if (changes.token || changes.authToken) {
        initializeComponent();
      }
      // Refresh storage when posts are saved or deleted (which might affect cloud storage)
      if (changes.savedPosts || changes.localSavedPosts) {
        if (isAuthenticated) {
          // Debounce the refresh to avoid too many API calls
          setTimeout(() => {
            fetchStorageUsage();
          }, 1000);
        }
      }
    };

    chrome.storage.onChanged.addListener(handleStorageChange);

    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange);
    };
  }, []);

  const fetchStorageUsage = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getStorageUsage();
      setStorageData(data);
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to load storage usage';
      setError(errorMessage);
      console.error('Storage usage fetch error:', err);

      // If authentication error, update auth status
      if (errorMessage.includes('token') || errorMessage.includes('Authentication')) {
        setIsAuthenticated(false);
      }
    } finally {
      setLoading(false);
    }
  };

  // Don't render anything if user is not authenticated
  if (!isAuthenticated) {
    return null;
  }

  if (loading) {
    return (
      <div className={`notely-card bg-notely-card border border-notely-border rounded-notely-lg shadow-notely-md notely-breathing-md ${className}`}>
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <span className="text-lg">💾</span>
              <div className="h-4 bg-notely-surface rounded w-16"></div>
            </div>
            <div className="h-6 bg-notely-surface rounded-full w-12"></div>
          </div>
          <div className="h-2 bg-notely-surface rounded-full w-full mb-2"></div>
          <div className="h-3 bg-notely-surface rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (error || !storageData) {
    return (
      <div className={`notely-card bg-notely-card border border-notely-border rounded-notely-lg shadow-notely-md notely-breathing-md ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <span className="text-lg">💾</span>
            <h3 className="text-sm font-semibold text-notely-text-primary notely-heading">
              Storage
            </h3>
          </div>
          <span className="text-xs font-medium px-2 py-1 rounded-full bg-notely-coral/20 text-notely-coral border border-notely-coral/30">
            ERROR
          </span>
        </div>
        <div className="text-sm text-notely-text-secondary mb-4">
          {error || 'Storage usage unavailable'}
        </div>
        <button
          onClick={fetchStorageUsage}
          className="w-full text-xs font-medium py-2 px-3 rounded-notely-md transition-all duration-200 text-notely-sky hover:text-white hover:bg-notely-sky/20 border border-notely-sky/30 hover:border-notely-sky notely-body"
        >
          <div className="flex items-center justify-center space-x-2">
            <span>🔄</span>
            <span>Retry</span>
          </div>
        </button>
      </div>
    );
  }

  const progressColor = getStorageUsageColor(storageData.usagePercentage);
  const message = getStorageUsageMessage(storageData);
  const recommendedAction = getRecommendedAction(storageData);

  return (
    <div className={`notely-card bg-notely-card border border-notely-border rounded-notely-lg shadow-notely-md hover:shadow-notely-lg transition-all duration-200 notely-breathing-md ${className}`}>
      {/* Header with enhanced visual hierarchy */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <span className="text-lg">💾</span>
          <h3 className="text-sm font-semibold text-notely-text-primary notely-heading">
            Storage
          </h3>
        </div>
        <span className={`text-xs font-medium px-2 py-1 rounded-full ${
          storageData.plan === 'premium'
            ? 'bg-notely-mint/20 text-notely-mint border border-notely-mint/30'
            : 'bg-notely-surface text-notely-text-muted border border-notely-border'
        }`}>
          {storageData.plan.toUpperCase()}
        </span>
      </div>

      {/* Enhanced Progress Bar with color coding */}
      <div className="mb-4">
        <div className="flex justify-between text-xs text-notely-text-secondary mb-2 notely-body">
          <span className="font-medium">{storageData.usedMB} MB used</span>
          <span className="text-notely-text-muted">{storageData.limitMB} MB total</span>
        </div>

        {/* Progress bar with rounded corners and glow effect */}
        <div className="relative notely-progress bg-notely-surface rounded-full overflow-hidden">
          <div
            className="notely-progress-bar transition-all duration-500 ease-out rounded-full relative"
            style={{
              width: `${Math.min(storageData.usagePercentage, 100)}%`,
              backgroundColor: progressColor,
              boxShadow: storageData.usagePercentage > 75 ? `0 0 8px ${progressColor}40` : 'none'
            }}
          >
            {/* Animated shine effect for high usage */}
            {storageData.usagePercentage > 90 && (
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>
            )}
          </div>
        </div>

        <div className={`text-xs mt-2 text-center font-medium notely-body ${
          storageData.isOverLimit ? 'text-notely-coral' :
          storageData.isNearLimit ? 'text-yellow-400' :
          'text-notely-text-muted'
        }`}>
          {storageData.usagePercentage}% used
        </div>
      </div>

      {/* Enhanced Message Section */}
      {showDetails && (
        <div className="space-y-3">
          <p className={`text-xs notely-body ${
            storageData.isOverLimit
              ? 'text-notely-coral'
              : storageData.isNearLimit
                ? 'text-yellow-400'
                : 'text-notely-text-secondary'
          }`}>
            {message}
          </p>

          {/* Recommended Action with better styling */}
          {recommendedAction && (
            <div className={`text-xs p-3 rounded-notely-md border-l-4 ${
              storageData.isOverLimit
                ? 'bg-notely-coral/10 text-notely-coral border-l-notely-coral border-notely-coral/20'
                : 'bg-notely-sky/10 text-notely-sky border-l-notely-sky border-notely-sky/20'
            }`}>
              <div className="flex items-start space-x-2">
                <span className="text-sm">💡</span>
                <span className="font-medium">{recommendedAction}</span>
              </div>
            </div>
          )}

          {/* Feature highlight */}
          <div className="text-xs text-notely-text-muted bg-notely-surface p-3 rounded-notely-md border border-notely-border">
            <div className="flex items-center space-x-2">
              <span className="text-sm">🖼️</span>
              <span>Images auto-preserved for deleted posts</span>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Refresh Button */}
      <button
        onClick={() => {
          if (!loading) {
            fetchStorageUsage();
          }
        }}
        className={`mt-4 w-full text-xs font-medium py-2 px-3 rounded-notely-md transition-all duration-200 notely-body ${
          loading
            ? 'bg-notely-surface text-notely-text-muted cursor-not-allowed opacity-50'
            : 'text-notely-sky hover:text-white hover:bg-notely-sky/20 border border-notely-sky/30 hover:border-notely-sky hover:shadow-sm'
        }`}
        disabled={loading}
        title={loading ? 'Refreshing storage usage...' : 'Refresh storage usage'}
      >
        {loading ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="w-3 h-3 border-2 border-notely-text-muted border-t-transparent rounded-full animate-spin"></div>
            <span>Refreshing...</span>
          </div>
        ) : (
          <div className="flex items-center justify-center space-x-2">
            <span className="transition-transform duration-200 hover:rotate-180">🔄</span>
            <span>Refresh Storage</span>
          </div>
        )}
      </button>
    </div>
  );
};

// Export the storage usage fetching function for use by other components
export { getStorageUsage, getAuthToken };

export default StorageUsage;
