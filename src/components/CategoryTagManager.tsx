import React, { useState, useEffect, useCallback } from 'react';
import { Post } from '../types';
import { getAllCategories, saveAllCategories, getAllTags, saveAllTags, getSavedPosts, savePost } from '../storage';
import { formatForDisplay } from '../utils/formatUtils';
import Widget from './Widget';
import { useTranslation } from '../hooks/useTranslation';

interface CategoryTagManagerProps {
  posts: Post[];
  isDragging?: boolean;
  onRemove?: () => void;
}

interface CategoryItem {
  id: string;
  name: string;
  postCount: number;
  isEditing: boolean;
  isUserCreated: boolean;
}

interface TagItem {
  id: string;
  name: string;
  postCount: number;
  isEditing: boolean;
  isUserCreated: boolean;
}

const CategoryTagManager: React.FC<CategoryTagManagerProps> = ({
  posts,
  isDragging = false,
  onRemove
}) => {
  const { t } = useTranslation();
  const [activeView, setActiveView] = useState<'categories' | 'category-detail'>('categories');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [categories, setCategories] = useState<CategoryItem[]>([]);
  const [tags, setTags] = useState<TagItem[]>([]);
  const [draggedItem, setDraggedItem] = useState<string | null>(null);
  const [editingValue, setEditingValue] = useState<string>('');
  const [showAddForm, setShowAddForm] = useState<boolean>(false);
  const [newItemName, setNewItemName] = useState<string>('');

  console.log('[CategoryTagManager] Component re-rendered. Current categories state (first 3):', JSON.stringify(categories.slice(0, 3), null, 2));

  // Load categories and tags from storage and posts
  const loadCategoriesAndTags = useCallback(async () => {
    console.log(`[CategoryTagManager] ENTERING loadCategoriesAndTags. Posts length: ${posts.length}`);
    try {
      // Get stored categories and tags
      console.log('[CategoryTagManager] BEFORE Promise.all for stored C/T.');
      const [storedCategories, storedTags] = await Promise.all([
        getAllCategories(),
        getAllTags()
      ]);
      console.log('[CategoryTagManager] Stored categories:', storedCategories);
      console.log('[CategoryTagManager] Stored tags:', storedTags);

      // Extract categories and tags from posts
      const postCategories = new Set<string>();
      const postTags = new Set<string>();
      const categoryCounts = new Map<string, number>();
      const tagCounts = new Map<string, number>();
      
      // Create a map to track which posts belong to which categories and tags
      const categoryToPostsMap = new Map<string, Set<string>>();
      const tagToPostsMap = new Map<string, Set<string>>();
      
      // Process each post
      console.log('[CategoryTagManager] Processing posts for counts. Number of posts:', posts.length);
      posts.forEach(post => {
        const postId = post.id || Math.random().toString(); // Fallback if post has no ID
        
        // Process categories
        if (post.categories && Array.isArray(post.categories)) {
          post.categories.forEach(cat => {
            if (typeof cat === 'string' && cat.trim()) {
              // Add category to the set of unique categories
              postCategories.add(cat);
              
              // Add this post to the set of posts for this category
              if (!categoryToPostsMap.has(cat)) {
                categoryToPostsMap.set(cat, new Set<string>());
              }
              categoryToPostsMap.get(cat)!.add(postId);
            }
          });
        }
        
        // Process tags
        if (post.tags && Array.isArray(post.tags)) {
          post.tags.forEach(tag => {
            if (typeof tag === 'string' && tag.trim()) {
              // Add tag to the set of unique tags
              postTags.add(tag);
              
              // Add this post to the set of posts for this tag
              if (!tagToPostsMap.has(tag)) {
                tagToPostsMap.set(tag, new Set<string>());
              }
              tagToPostsMap.get(tag)!.add(postId);
            }
          });
        }
      });
      
      // Calculate counts based on the number of unique posts per category/tag
      categoryToPostsMap.forEach((postIds, category) => {
        categoryCounts.set(category, postIds.size);
      });
      
      tagToPostsMap.forEach((postIds, tag) => {
        tagCounts.set(tag, postIds.size);
      });
      console.log('[CategoryTagManager] categoryToPostsMap:', categoryToPostsMap);
      console.log('[CategoryTagManager] tagToPostsMap:', tagToPostsMap);
      console.log('[CategoryTagManager] Calculated categoryCounts:', categoryCounts);
      console.log('[CategoryTagManager] Calculated tagCounts:', tagCounts);

      // Merge stored and post-derived categories
      const allCategories = new Set([...storedCategories, ...postCategories]);
      console.log('[CategoryTagManager] Merged allCategories (stored + from posts):', allCategories);
      const categoryItems: CategoryItem[] = Array.from(allCategories).map(catName => {
        const countFromMap = categoryCounts.get(catName);
        const finalCount = countFromMap === undefined ? 0 : countFromMap;
        console.log(`[CategoryTagManager] Mapping category: '${catName}'. Raw count from categoryCounts.get('${catName}'): ${countFromMap}. Final postCount for item: ${finalCount}.`);
        // For specific categories, log the entire categoryCounts map to see its state
        if (catName === 'ai_data' || catName === 'technology' || catName === 'career_productivity') {
            console.log(`[CategoryTagManager] Full categoryCounts map when processing '${catName}':`, new Map(categoryCounts));
        }
        return {
          id: catName,
          name: catName,
          postCount: finalCount,
          isEditing: false,
          isUserCreated: storedCategories.includes(catName)
        };
      });

      // Merge stored and post-derived tags
      const allTags = new Set([...storedTags, ...postTags]);
      console.log('[CategoryTagManager] Merged allTags (stored + from posts):', allTags);
      const tagItems: TagItem[] = Array.from(allTags).map(tag => ({
        id: tag,
        name: tag,
        postCount: tagCounts.get(tag) || 0,
        isEditing: false,
        isUserCreated: storedTags.includes(tag)
      }));

      // Sort by post count (descending) then alphabetically
      categoryItems.sort((a, b) => {
        if (b.postCount !== a.postCount) return b.postCount - a.postCount;
        return a.name.localeCompare(b.name);
      });

      tagItems.sort((a, b) => {
        if (b.postCount !== a.postCount) return b.postCount - a.postCount;
        return a.name.localeCompare(b.name);
      });

      console.log('[CategoryTagManager] Final categoryItems to be set to state (SNAPSHOT - first 5):', JSON.stringify(categoryItems.slice(0, 5), null, 2));
      console.log('[CategoryTagManager] Final tagItems to be set to state (first 5):', tagItems.slice(0, 5).map(ti => ({ name: ti.name, count: ti.postCount })));
      setCategories(categoryItems);
      setTags(tagItems);
      console.log('[CategoryTagManager] AFTER setCategories. categoryItems length:', categoryItems.length); // Log after set
    } catch (error) {
      console.error("[CategoryTagManager] ERROR in loadCategoriesAndTags:", error);
    }
  }, [posts]);

  useEffect(() => {
    console.log(`[CategoryTagManager] useEffect triggered. Posts length: ${posts.length}. Calling loadCategoriesAndTags.`);
    console.log(`[CategoryTagManager] Inspecting posts prop inside useEffect (first 3):`, JSON.stringify(posts.slice(0, 3), null, 2));
    loadCategoriesAndTags();
  }, [loadCategoriesAndTags, posts]);

  // Start editing an item
  const startEditing = (id: string, currentName: string) => {
    if (activeView === 'categories') {
      setCategories(prev => prev.map(cat =>
        cat.id === id ? { ...cat, isEditing: true } : { ...cat, isEditing: false }
      ));
    } else {
      setTags(prev => prev.map(tag =>
        tag.id === id ? { ...tag, isEditing: true } : { ...tag, isEditing: false }
      ));
    }
    setEditingValue(currentName);
  };

  // Update posts when category/tag names change
  const updatePostsWithNewName = async (oldName: string, newName: string, type: 'categories' | 'tags') => {
    try {
      const allPosts = await getSavedPosts();
      const updatedPosts: Post[] = [];

      for (const post of allPosts) {
        let needsUpdate = false;
        const updatedPost = { ...post };

        if (type === 'categories' && post.categories) {
          const updatedCategories = post.categories.map(cat => {
            if (cat === oldName) {
              needsUpdate = true;
              return newName;
            }
            return cat;
          });
          if (needsUpdate) {
            updatedPost.categories = updatedCategories;
          }
        } else if (type === 'tags' && post.tags) {
          const updatedTags = post.tags.map(tag => {
            if (tag === oldName) {
              needsUpdate = true;
              return newName;
            }
            return tag;
          });
          if (needsUpdate) {
            updatedPost.tags = updatedTags;
          }
        }

        if (needsUpdate) {
          updatedPosts.push(updatedPost);
        }
      }

      // Save all updated posts
      for (const post of updatedPosts) {
        await savePost(post);
      }

      console.log(`[CategoryTagManager] Updated ${updatedPosts.length} posts with new ${type.slice(0, -1)} name: ${oldName} → ${newName}`);
    } catch (error) {
      console.error('[CategoryTagManager] Error updating posts with new name:', error);
    }
  };

  // Save edited item
  const saveEdit = async (id: string) => {
    const newName = editingValue.trim();
    if (!newName || newName === id) {
      cancelEdit();
      return;
    }

    try {
      if (activeView === 'categories') {
        const updatedCategories = categories.map(cat =>
          cat.id === id ? { ...cat, name: newName, id: newName, isEditing: false, isUserCreated: true } : cat
        );
        setCategories(updatedCategories);

        // Update posts with the new category name
        await updatePostsWithNewName(id, newName, 'categories');

        // Save to storage
        const categoryNames = updatedCategories.map(cat => cat.name);
        await saveAllCategories(categoryNames);
      } else {
        const updatedTags = tags.map(tag =>
          tag.id === id ? { ...tag, name: newName, id: newName, isEditing: false, isUserCreated: true } : tag
        );
        setTags(updatedTags);

        // Update posts with the new tag name
        await updatePostsWithNewName(id, newName, 'tags');

        // Save to storage
        const tagNames = updatedTags.map(tag => tag.name);
        await saveAllTags(tagNames);
      }
    } catch (error) {
      console.error('[CategoryTagManager] Error saving edit:', error);
    }

    setEditingValue('');
  };

  // Cancel editing
  const cancelEdit = () => {
    if (activeView === 'categories') {
      setCategories(prev => prev.map(cat => ({ ...cat, isEditing: false })));
    } else {
      setTags(prev => prev.map(tag => ({ ...tag, isEditing: false })));
    }
    setEditingValue('');
  };

  // Delete an item
  const deleteItem = async (id: string) => {
    try {
      if (activeView === 'categories') {
        const updatedCategories = categories.filter(cat => cat.id !== id);
        setCategories(updatedCategories);

        const categoryNames = updatedCategories.map(cat => cat.name);
        await saveAllCategories(categoryNames);
      } else {
        const updatedTags = tags.filter(tag => tag.id !== id);
        setTags(updatedTags);

        const tagNames = updatedTags.map(tag => tag.name);
        await saveAllTags(tagNames);
      }
    } catch (error) {
      console.error('[CategoryTagManager] Error deleting item:', error);
    }
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, itemId: string) => {
    setDraggedItem(itemId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = async (e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    
    if (!draggedItem || draggedItem === targetId) {
      setDraggedItem(null);
      return;
    }

    try {
      if (activeView === 'categories') {
        const draggedIndex = categories.findIndex(cat => cat.id === draggedItem);
        const targetIndex = categories.findIndex(cat => cat.id === targetId);

        if (draggedIndex !== -1 && targetIndex !== -1) {
          const newCategories = [...categories];
          const [draggedCategory] = newCategories.splice(draggedIndex, 1);
          newCategories.splice(targetIndex, 0, draggedCategory);

          setCategories(newCategories);

          // Save new order
          const categoryNames = newCategories.map(cat => cat.name);
          await saveAllCategories(categoryNames);
        }
      } else {
        const draggedIndex = tags.findIndex(tag => tag.id === draggedItem);
        const targetIndex = tags.findIndex(tag => tag.id === targetId);

        if (draggedIndex !== -1 && targetIndex !== -1) {
          const newTags = [...tags];
          const [draggedTag] = newTags.splice(draggedIndex, 1);
          newTags.splice(targetIndex, 0, draggedTag);

          setTags(newTags);

          // Save new order
          const tagNames = newTags.map(tag => tag.name);
          await saveAllTags(tagNames);
        }
      }
    } catch (error) {
      console.error('[CategoryTagManager] Error reordering items:', error);
    }
    
    setDraggedItem(null);
  };

  // Add new item
  const addNewItem = async () => {
    const name = newItemName.trim();
    if (!name) return;

    try {
      if (activeView === 'categories') {
        const newCategory: CategoryItem = {
          id: name,
          name: name,
          postCount: 0,
          isEditing: false,
          isUserCreated: true
        };
        const updatedCategories = [...categories, newCategory];
        setCategories(updatedCategories);

        const categoryNames = updatedCategories.map(cat => cat.name);
        await saveAllCategories(categoryNames);
      } else {
        const newTag: TagItem = {
          id: name,
          name: name,
          postCount: 0,
          isEditing: false,
          isUserCreated: true
        };
        const updatedTags = [...tags, newTag];
        setTags(updatedTags);

        const tagNames = updatedTags.map(tag => tag.name);
        await saveAllTags(tagNames);
      }

      setNewItemName('');
      setShowAddForm(false);
    } catch (error) {
      console.error('[CategoryTagManager] Error adding new item:', error);
    }
  };

  // Get tags for a specific category
  const getTagsForCategory = useCallback((categoryName: string) => {
    const relevantTagNames = new Set<string>();
    posts.forEach(post => {
      if (post.categories && post.categories.includes(categoryName)) {
        if (post.tags) {
          post.tags.forEach(tag => relevantTagNames.add(tag));
        }
      }
    });

    // Filter the main `tags` state to get only the relevant TagItems
    const filteredTags = tags.filter(tag => relevantTagNames.has(tag.name));
    return filteredTags;
  }, [posts, tags]); // Dependencies for useCallback

  const renderCategoryCard = (item: CategoryItem) => {
    console.log('[CategoryTagManager] renderCategoryCard called for item:', JSON.stringify(item));
    const isDraggingThis = draggedItem === item.id;
    const categoryTags = getTagsForCategory(item.name);

    return (
      <div
        key={item.id}
        className={`
          group relative bg-notely-card rounded-xl border-2 border-notely-border hover:border-notely-accent/30
          transition-all duration-200 hover:shadow-notely-lg hover:-translate-y-0.5
          ${isDraggingThis ? 'opacity-50 scale-95 rotate-2' : ''}
          ${item.isEditing ? 'border-notely-accent shadow-notely-lg ring-4 ring-notely-accent/20' : ''}
        `}
        draggable={!item.isEditing}
        onDragStart={(e) => handleDragStart(e, item.id)}
        onDragOver={handleDragOver}
        onDrop={(e) => handleDrop(e, item.id)}
      >
        {/* Drag handle - top right corner */}
        {!item.isEditing && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity cursor-move">
            <div className="p-1 rounded-md bg-notely-surface hover:bg-notely-border transition-colors">
              <svg className="w-3 h-3 text-notely-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8h16M4 16h16" />
              </svg>
            </div>
          </div>
        )}

        <div className="p-4">
          {/* Header with icon and post count */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-notely-mint"></div>
              <span className="text-xs font-medium text-notely-text-muted uppercase tracking-wide">
                {item.postCount} {item.postCount === 1 ? t('widgets.post') : t('widgets.posts')}
              </span>
            </div>

            {item.isUserCreated && (
              <div className="flex items-center space-x-1 bg-notely-accent/10 px-2 py-1 rounded-full">
                <svg className="w-3 h-3 text-notely-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
                <span className="text-xs font-medium text-notely-accent">{t('widgets.custom')}</span>
              </div>
            )}
          </div>

          {/* Name (editable) */}
          {item.isEditing ? (
            <div className="space-y-3">
              <input
                type="text"
                value={editingValue}
                onChange={(e) => setEditingValue(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') saveEdit(item.id);
                  if (e.key === 'Escape') cancelEdit();
                }}
                className="w-full px-3 py-2 text-lg font-semibold border-2 border-notely-accent rounded-lg focus:outline-none transition-colors bg-notely-card text-notely-text-primary placeholder-notely-text-muted"
                placeholder={`Edit "${formatForDisplay(item.name)}"...`}
                autoFocus
                style={{
                  backgroundColor: 'var(--notely-card)',
                  color: 'var(--notely-text-primary)',
                  borderColor: 'var(--notely-accent)'
                }}
              />
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => saveEdit(item.id)}
                  className="flex-1 px-3 py-2 bg-notely-accent text-white rounded-lg hover:bg-notely-accent/90 transition-colors text-sm font-medium"
                >
                  {t('widgets.save')}
                </button>
                <button
                  onClick={cancelEdit}
                  className="flex-1 px-3 py-2 bg-notely-surface text-notely-text-secondary rounded-lg hover:bg-notely-border transition-colors text-sm font-medium"
                >
                  {t('widgets.cancel')}
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <h3
                className="text-lg font-semibold text-notely-text-primary cursor-pointer hover:text-notely-accent transition-colors leading-tight"
                onClick={() => startEditing(item.id, item.name)}
                title="Click to edit"
              >
                {formatForDisplay(item.name)}
              </h3>

              {/* Related tags preview */}
              {categoryTags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {categoryTags.slice(0, 3).map(tag => (
                    <span
                      key={tag.id}
                      className="px-2 py-1 bg-notely-coral/10 text-notely-coral text-xs rounded-full"
                    >
                      {formatForDisplay(tag.name)}
                    </span>
                  ))}
                  {categoryTags.length > 3 && (
                    <span className="px-2 py-1 bg-notely-surface text-notely-text-muted text-xs rounded-full">
                      +{categoryTags.length - 3} more
                    </span>
                  )}
                </div>
              )}

              {/* Action buttons */}
              <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                <button
                  onClick={() => startEditing(item.id, item.name)}
                  className="flex items-center space-x-1 px-3 py-1.5 bg-notely-surface hover:bg-notely-accent hover:text-white rounded-lg transition-all text-sm font-medium text-notely-text-secondary"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                  <span>{t('widgets.edit')}</span>
                </button>

                <button
                  onClick={() => {
                    setSelectedCategory(item.name);
                    setActiveView('category-detail');
                  }}
                  className="flex items-center space-x-1 px-3 py-1.5 bg-notely-surface hover:bg-notely-mint hover:text-white rounded-lg transition-all text-sm font-medium text-notely-text-secondary"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                  <span>Tags</span>
                </button>

                {item.isUserCreated && (
                  <button
                    onClick={() => deleteItem(item.id)}
                    className="flex items-center space-x-1 px-3 py-1.5 bg-red-50 hover:bg-red-500 text-red-600 hover:text-white rounded-lg transition-all text-sm font-medium"
                  >
                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    <span>Delete</span>
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <h2 className="text-xl font-bold text-notely-text-primary mb-2 notely-heading">Organize Your Content</h2>
          <p className="text-sm text-notely-text-secondary notely-body">
            Manage categories and their related tags to keep your saved content organized.
          </p>
        </div>

        {/* Navigation */}
        {activeView === 'category-detail' && selectedCategory ? (
          <div className="flex items-center justify-between">
            <button
              onClick={() => {
                setActiveView('categories');
                setSelectedCategory(null);
              }}
              className="flex items-center space-x-2 px-3 py-2 bg-notely-surface hover:bg-notely-card rounded-lg transition-colors text-notely-text-secondary hover:text-notely-text-primary"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              <span>Back to Categories</span>
            </button>
            <h3 className="text-lg font-semibold text-notely-text-primary notely-heading">
              Tags for "{formatForDisplay(selectedCategory)}"
            </h3>
          </div>
        ) : (
          <div className="flex bg-notely-surface rounded-xl p-1.5 max-w-md mx-auto">
            <div className="flex-1 px-4 py-2.5 text-sm font-semibold rounded-lg bg-notely-card text-notely-mint shadow-notely-md">
              <div className="flex items-center justify-center space-x-2">
                <div className="w-2 h-2 rounded-full bg-notely-mint"></div>
                <span>Categories</span>
                <span className="bg-notely-surface text-notely-text-muted px-2 py-0.5 rounded-full text-xs">
                  {categories.length}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Add New Button */}
        <div className="flex justify-center">
          {!showAddForm ? (
            <button
              onClick={() => setShowAddForm(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-notely-accent to-notely-mint text-white rounded-lg hover:shadow-notely-lg transition-all font-medium"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              <span>Add New {activeView === 'category-detail' ? 'Tag' : 'Category'}</span>
            </button>
          ) : (
            <div className="bg-notely-card rounded-xl border-2 border-notely-accent p-4 w-full max-w-md">
              <h3 className="text-lg font-semibold text-notely-text-primary mb-3 notely-heading">
                Create New {activeView === 'category-detail' ? 'Tag' : 'Category'}
              </h3>
              <div className="space-y-3">
                <input
                  type="text"
                  value={newItemName}
                  onChange={(e) => setNewItemName(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') addNewItem();
                    if (e.key === 'Escape') {
                      setShowAddForm(false);
                      setNewItemName('');
                    }
                  }}
                  placeholder={`Enter ${activeView === 'category-detail' ? 'tag' : 'category'} name...`}
                  className="w-full px-3 py-2 border-2 border-notely-border rounded-lg focus:outline-none focus:border-notely-accent transition-colors bg-notely-surface text-notely-text-primary placeholder-notely-text-muted"
                  autoFocus
                  style={{
                    backgroundColor: 'var(--notely-surface)',
                    color: 'var(--notely-text-primary)',
                    borderColor: 'var(--notely-border)'
                  }}
                />
                <div className="flex items-center space-x-2">
                  <button
                    onClick={addNewItem}
                    disabled={!newItemName.trim()}
                    className="flex-1 px-3 py-2 bg-notely-accent text-white rounded-lg hover:bg-notely-accent/90 transition-colors text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Create
                  </button>
                  <button
                    onClick={() => {
                      setShowAddForm(false);
                      setNewItemName('');
                    }}
                    className="flex-1 px-3 py-2 bg-notely-surface text-notely-text-secondary rounded-lg hover:bg-notely-border transition-colors text-sm font-medium"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Items Grid */}
        <div className="max-h-80 overflow-y-auto">
          {activeView === 'categories' ? (
            categories.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {categories.map(renderCategoryCard)}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 bg-notely-surface rounded-full flex items-center justify-center">
                  <span className="text-2xl">📂</span>
                </div>
                <h3 className="text-lg font-semibold text-notely-text-primary mb-2 notely-heading">
                  No categories yet
                </h3>
                <p className="text-sm text-notely-text-secondary max-w-sm mx-auto mb-4 notely-body">
                  Start saving posts from social media and we'll automatically create categories for you.
                  You can then edit and organize them here.
                </p>
                <p className="text-sm text-notely-text-muted notely-body">
                  Or create your first category using the button above.
                </p>
              </div>
            )
          ) : (
            // Category detail view - show tags for selected category
            <div className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                {tags.map(tag => (
                  <div
                    key={tag.id}
                    className="group bg-notely-card rounded-lg border border-notely-border hover:border-notely-coral/30 p-3 transition-all hover:shadow-notely-md"
                  >
                    {tag.isEditing ? (
                      // Editing mode for tags
                      <div className="space-y-2">
                        <input
                          type="text"
                          value={editingValue}
                          onChange={(e) => setEditingValue(e.target.value)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') saveEdit(tag.id);
                            if (e.key === 'Escape') cancelEdit();
                          }}
                          className="w-full px-2 py-1 text-sm font-medium border border-notely-accent rounded focus:outline-none transition-colors bg-notely-card text-notely-text-primary placeholder-notely-text-muted"
                          placeholder={`Edit "${formatForDisplay(tag.name)}"...`}
                          autoFocus
                          style={{
                            backgroundColor: 'var(--notely-card)',
                            color: 'var(--notely-text-primary)',
                            borderColor: 'var(--notely-accent)'
                          }}
                        />
                        <div className="flex items-center space-x-1">
                          <button
                            onClick={() => saveEdit(tag.id)}
                            className="flex-1 px-2 py-1 bg-notely-coral text-white rounded text-xs hover:bg-notely-coral/90 transition-colors"
                          >
                            Save
                          </button>
                          <button
                            onClick={cancelEdit}
                            className="flex-1 px-2 py-1 bg-notely-surface text-notely-text-secondary rounded text-xs hover:bg-notely-border transition-colors"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    ) : (
                      // Display mode for tags
                      <>
                        <div className="flex items-center justify-between mb-2">
                          <span
                            className="text-sm font-medium text-notely-text-primary cursor-pointer hover:text-notely-coral transition-colors"
                            onClick={() => startEditing(tag.id, tag.name)}
                            title="Click to edit"
                          >
                            {formatForDisplay(tag.name)}
                          </span>
                          <span className="text-xs text-notely-text-muted bg-notely-surface px-2 py-1 rounded-full">
                            {tag.postCount}
                          </span>
                        </div>
                        <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <button
                            onClick={() => startEditing(tag.id, tag.name)}
                            className="flex-1 px-2 py-1 bg-notely-surface hover:bg-notely-coral hover:text-white rounded text-xs transition-all text-notely-text-secondary"
                          >
                            Edit
                          </button>
                          {tag.isUserCreated && (
                            <button
                              onClick={() => deleteItem(tag.id)}
                              className="px-2 py-1 bg-red-50 hover:bg-red-500 text-red-600 hover:text-white rounded text-xs transition-all"
                            >
                              Delete
                            </button>
                          )}
                        </div>
                      </>
                    )}
                  </div>
                ))}
              </div>

              {tags.length === 0 && (
                <div className="text-center py-8">
                  <div className="w-12 h-12 mx-auto mb-3 bg-notely-surface rounded-full flex items-center justify-center">
                    <span className="text-xl">🏷️</span>
                  </div>
                  <h4 className="text-md font-semibold text-notely-text-primary mb-2 notely-heading">
                    No tags yet
                  </h4>
                  <p className="text-sm text-notely-text-secondary notely-body">
                    Tags help you organize posts within this category. Create your first tag above.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Quick Tips */}
        {(categories.length > 0 || tags.length > 0) && (
          <div className="bg-notely-surface/50 rounded-xl p-4 border border-notely-border">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-notely-accent/10 rounded-lg flex items-center justify-center flex-shrink-0">
                <svg className="w-4 h-4 text-notely-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="flex-1">
                <h4 className="text-sm font-semibold text-notely-text-primary mb-1 notely-heading">Pro Tips</h4>
                <ul className="text-xs text-notely-text-secondary space-y-1 notely-body">
                  {activeView === 'categories' ? (
                    <>
                      <li>• Click any category name to edit it instantly</li>
                      <li>• Click "Tags" button to manage tags for that category</li>
                      <li>• Drag the handle (⋮⋮) in the corner to reorder</li>
                      <li>• Your edits will update all related posts automatically</li>
                    </>
                  ) : (
                    <>
                      <li>• Click any tag name to edit it instantly</li>
                      <li>• Tags help organize posts within the selected category</li>
                      <li>• Your edits will update all related posts automatically</li>
                    </>
                  )}
                </ul>
              </div>
            </div>
          </div>
        )}
    </div>
  );
};

export default CategoryTagManager;
