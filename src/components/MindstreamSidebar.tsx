import React from 'react';
import { Post } from '../types';
import DailyWisdom from './DailyWisdom';

interface MindstreamSidebarProps {
  posts: Post[];
  className?: string;
}

const MindstreamSidebar: React.FC<MindstreamSidebarProps> = ({
  posts,
  className = ''
}) => {
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Daily Wisdom */}
      <div>
        <DailyWisdom
          className="w-full"
          onQuoteClick={(quote) => {
            console.log('Quote clicked:', quote);
          }}
        />
      </div>
    </div>
  );
};

export default MindstreamSidebar;
