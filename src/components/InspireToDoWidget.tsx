import React, { useState, useEffect } from 'react';
import { Post } from '../types';
import { useTranslation } from '../hooks/useTranslation';
import { toast } from '../utils/toast';
import { generateActionIdeas } from '../services/inspireToDoService';

interface InspireToDoWidgetProps {
  posts: Post[];
  isDragging: boolean;
  onRemove: () => void;
}

interface ActionIdea {
  id: string;
  title: string;
  suggestedTool: string;
  status: 'todo' | 'draft' | 'done';
  createdAt: Date;
}

type TaskStatus = 'todo' | 'draft' | 'done';

const InspireToDoWidget: React.FC<InspireToDoWidgetProps> = ({ posts, isDragging, onRemove }) => {
  const { t } = useTranslation();
  const [actionIdeas, setActionIdeas] = useState<ActionIdea[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Get last 10 saved bookmarks
  const getRecentBookmarks = (): Post[] => {
    return posts
      .sort((a, b) => new Date(b.savedAt || b.timestamp || '').getTime() - new Date(a.savedAt || a.timestamp || '').getTime())
      .slice(0, 10);
  };

  // Generate action ideas from bookmarks
  const generateIdeas = async () => {
    const recentBookmarks = getRecentBookmarks();
    
    if (recentBookmarks.length === 0) {
      setActionIdeas([]);
      return;
    }

    setIsLoading(true);
    try {
      const ideas = await generateActionIdeas(recentBookmarks);
      const actionIdeasWithIds = ideas.map((idea, index) => ({
        id: `idea-${Date.now()}-${index}`,
        ...idea,
        status: 'todo' as TaskStatus,
        createdAt: new Date()
      }));
      
      setActionIdeas(actionIdeasWithIds);
      setLastUpdated(new Date());
      
      // Store in localStorage for persistence
      localStorage.setItem('inspireToDoIdeas', JSON.stringify(actionIdeasWithIds));
      localStorage.setItem('inspireToDoLastUpdated', new Date().toISOString());
      
      toast.success('New action ideas generated! ✨');
    } catch (error) {
      console.error('Failed to generate action ideas:', error);
      toast.error('Failed to generate ideas. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Load persisted data on mount
  useEffect(() => {
    const savedIdeas = localStorage.getItem('inspireToDoIdeas');
    const savedLastUpdated = localStorage.getItem('inspireToDoLastUpdated');
    
    if (savedIdeas) {
      try {
        const parsedIdeas = JSON.parse(savedIdeas);
        setActionIdeas(parsedIdeas);
      } catch (error) {
        console.error('Failed to parse saved ideas:', error);
      }
    }
    
    if (savedLastUpdated) {
      setLastUpdated(new Date(savedLastUpdated));
    }
    
    // Auto-generate if no saved ideas or if it's been more than 24 hours
    const shouldAutoGenerate = !savedIdeas || 
      (savedLastUpdated && Date.now() - new Date(savedLastUpdated).getTime() > 24 * 60 * 60 * 1000);
    
    if (shouldAutoGenerate && posts.length > 0) {
      generateIdeas();
    }
  }, [posts.length]);

  // Cycle task status
  const cycleStatus = (ideaId: string) => {
    setActionIdeas(prev => {
      const updated = prev.map(idea => {
        if (idea.id === ideaId) {
          const statusCycle: Record<TaskStatus, TaskStatus> = {
            'todo': 'draft',
            'draft': 'done',
            'done': 'todo'
          };
          return { ...idea, status: statusCycle[idea.status] };
        }
        return idea;
      });
      
      // Persist updated status
      localStorage.setItem('inspireToDoIdeas', JSON.stringify(updated));
      return updated;
    });
  };

  // Open suggested tool
  const openTool = (suggestedTool: string) => {
    toast.info(`Opening ${suggestedTool}... 🚀`);
    // TODO: Implement tool opening logic
    console.log(`Opening tool: ${suggestedTool}`);
  };

  // Get status pill styling
  const getStatusPill = (status: TaskStatus) => {
    const styles = {
      todo: {
        icon: '⭕',
        text: 'To-Do',
        className: 'bg-gray-500/10 text-gray-400 border-gray-500/20'
      },
      draft: {
        icon: '✍️',
        text: 'Draft',
        className: 'bg-yellow-500/10 text-yellow-400 border-yellow-500/20'
      },
      done: {
        icon: '✅',
        text: 'Done',
        className: 'bg-green-500/10 text-green-400 border-green-500/20'
      }
    };
    return styles[status];
  };

  // Format time ago
  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  // Empty state
  if (posts.length === 0) {
    return (
      <div className="text-center py-8 max-w-80">
        <div className="mb-4">
          <span className="text-6xl">💡</span>
        </div>
        <h4 className="text-sm font-medium text-notely-text-primary mb-2">
          No inspiration yet!
        </h4>
        <p className="text-xs text-notely-text-secondary">
          Save posts to get inspired with actionable tasks
        </p>
      </div>
    );
  }

  return (
    <div className="max-w-80 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-xs font-semibold text-notely-text-secondary mb-1 uppercase tracking-wide">
            Inspire To-Do
          </h4>
          {lastUpdated && (
            <p className="text-xs text-notely-text-tertiary">
              Updated {formatTimeAgo(lastUpdated)}
            </p>
          )}
        </div>
        
        <button
          onClick={generateIdeas}
          disabled={isLoading}
          className="p-2 hover:bg-notely-surface rounded-notely-md transition-colors disabled:opacity-50"
          title="Refresh ideas"
        >
          <div className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`}>
            🔄
          </div>
        </button>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="text-center py-6">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-notely-accent mx-auto mb-2"></div>
          <p className="text-xs text-notely-text-secondary">Generating ideas...</p>
        </div>
      )}

      {/* Action Ideas */}
      {!isLoading && actionIdeas.length > 0 && (
        <div className="space-y-4">
          {actionIdeas.map((idea, index) => {
            const statusPill = getStatusPill(idea.status);
            
            return (
              <div
                key={idea.id}
                className="bg-notely-surface rounded-notely-lg p-4 border border-notely-border hover:border-notely-accent/30 transition-all duration-300 animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                {/* Main Content */}
                <div className="flex items-start gap-3 mb-3">
                  {/* Icon */}
                  <div className="w-8 h-8 bg-notely-accent/10 rounded-notely-md flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-sm">🧠</span>
                  </div>
                  
                  {/* Title */}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-notely-text-primary font-medium line-clamp-2 leading-relaxed">
                      {idea.title}
                    </p>
                  </div>
                  
                  {/* Status Pill */}
                  <button
                    onClick={() => cycleStatus(idea.id)}
                    className={`
                      flex items-center space-x-1 px-2 py-1 rounded-notely-full border text-xs font-medium
                      transition-all duration-200 hover:scale-105 flex-shrink-0
                      ${statusPill.className}
                    `}
                  >
                    <span>{statusPill.icon}</span>
                    <span>{statusPill.text}</span>
                  </button>
                </div>
                
                {/* Tool Button */}
                <button
                  onClick={() => openTool(idea.suggestedTool)}
                  className="w-full text-left px-3 py-2 bg-notely-bg rounded-notely-md border border-notely-border hover:border-notely-accent/30 transition-colors"
                >
                  <span className="text-xs text-notely-text-secondary">
                    Open in <span className="text-notely-accent font-medium">{idea.suggestedTool}</span>
                  </span>
                </button>
              </div>
            );
          })}
        </div>
      )}

      {/* No Ideas State */}
      {!isLoading && actionIdeas.length === 0 && posts.length > 0 && (
        <div className="text-center py-6">
          <span className="text-4xl mb-2 block">🤔</span>
          <p className="text-xs text-notely-text-secondary mb-3">
            No action ideas yet
          </p>
          <button
            onClick={generateIdeas}
            className="notely-btn-primary text-xs px-3 py-2"
          >
            Generate Ideas
          </button>
        </div>
      )}
    </div>
  );
};

export default InspireToDoWidget;
