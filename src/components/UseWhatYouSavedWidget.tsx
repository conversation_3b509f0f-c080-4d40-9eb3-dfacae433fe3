import React, { useState, useEffect, useMemo } from 'react';
import { Post } from '../types';
import { ContentSuggestion } from '../types/contentSuggestions';
import { PlatformLogo } from './PlatformLogo';
import { analyzePostsForContentSuggestions } from '../utils/contentSuggestionUtils';
import { generateContentSuggestion } from '../services/aiService';
import { trackContentConversion } from '../services/contentConversionService';
import { useTranslation } from '../hooks/useTranslation';
import { toast } from '../utils/toast';

interface UseWhatYouSavedWidgetProps {
  posts: Post[];
  isDragging: boolean;
  onRemove: () => void;
}

interface GeneratedContent {
  suggestionId: string;
  content: string;
  isGenerating: boolean;
}

const UseWhatYouSavedWidget: React.FC<UseWhatYouSavedWidgetProps> = ({
  posts,
  isDragging,
  onRemove
}) => {
  const { t } = useTranslation();
  const [suggestions, setSuggestions] = useState<ContentSuggestion[]>([]);
  const [generatedContent, setGeneratedContent] = useState<Map<string, GeneratedContent>>(new Map());
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [lastAnalyzedCount, setLastAnalyzedCount] = useState(0);

  // Analyze posts for content suggestions
  const analyzePosts = useMemo(() => {
    if (posts.length === 0 || posts.length === lastAnalyzedCount) {
      return suggestions;
    }

    setIsAnalyzing(true);
    const newSuggestions = analyzePostsForContentSuggestions(posts);
    setSuggestions(newSuggestions);
    setLastAnalyzedCount(posts.length);
    setIsAnalyzing(false);
    
    return newSuggestions;
  }, [posts.length, lastAnalyzedCount]);

  // Generate content for a suggestion
  const handleGenerate = async (suggestion: ContentSuggestion) => {
    const postContent = suggestion.originalPost.content || suggestion.originalPost.text || '';
    
    if (!postContent || postContent.length < 20) {
      toast.error('Post content is too short to generate suggestions');
      return;
    }

    // Set generating state
    setGeneratedContent(prev => new Map(prev.set(suggestion.id, {
      suggestionId: suggestion.id,
      content: '',
      isGenerating: true
    })));

    try {
      const content = await generateContentSuggestion(
        postContent,
        suggestion.originalPost.platform || 'Web',
        suggestion.type
      );

      setGeneratedContent(prev => new Map(prev.set(suggestion.id, {
        suggestionId: suggestion.id,
        content,
        isGenerating: false
      })));

      // Track the content conversion
      await trackContentConversion(
        suggestion.type,
        suggestion.originalPost.id || 'unknown',
        content
      );

      toast.success('Content generated successfully!');
    } catch (error) {
      console.error('Error generating content:', error);
      toast.error('Failed to generate content. Please try again.');
      
      // Remove generating state on error
      setGeneratedContent(prev => {
        const newMap = new Map(prev);
        newMap.delete(suggestion.id);
        return newMap;
      });
    }
  };

  // Save content as draft
  const handleSaveAsDraft = (suggestion: ContentSuggestion) => {
    const generated = generatedContent.get(suggestion.id);
    if (!generated?.content) {
      toast.error('No content to save. Generate content first.');
      return;
    }

    // For now, copy to clipboard as we don't have a draft system
    navigator.clipboard.writeText(generated.content).then(() => {
      toast.success('Content copied to clipboard!');
    }).catch(() => {
      toast.error('Failed to copy content');
    });
  };

  // Schedule content (placeholder functionality)
  const handleSchedule = (suggestion: ContentSuggestion) => {
    const generated = generatedContent.get(suggestion.id);
    if (!generated?.content) {
      toast.error('No content to schedule. Generate content first.');
      return;
    }

    // Placeholder for scheduling functionality
    toast.info('Scheduling feature coming soon! Content copied to clipboard.');
    navigator.clipboard.writeText(generated.content);
  };

  // Format post preview text
  const getPostPreview = (post: Post): string => {
    const content = post.content || post.text || '';
    return content.length > 100 ? content.substring(0, 100) + '...' : content;
  };

  // Get confidence color
  const getConfidenceColor = (confidence: number): string => {
    if (confidence > 0.8) return 'text-green-500';
    if (confidence > 0.6) return 'text-yellow-500';
    return 'text-orange-500';
  };

  // Get engagement color
  const getEngagementColor = (engagement?: string): string => {
    switch (engagement) {
      case 'high': return 'bg-green-500/20 text-green-400';
      case 'medium': return 'bg-yellow-500/20 text-yellow-400';
      case 'low': return 'bg-orange-500/20 text-orange-400';
      default: return 'bg-notely-surface text-notely-text-secondary';
    }
  };

  if (isAnalyzing) {
    return (
      <div className="flex-1 flex items-center justify-center text-center py-8">
        <div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-notely-accent mx-auto mb-2"></div>
          <p className="text-sm text-notely-text-secondary">Analyzing your saved content...</p>
          <p className="text-xs text-notely-text-tertiary mt-1">Finding repurposing opportunities</p>
        </div>
      </div>
    );
  }

  if (suggestions.length === 0) {
    return (
      <div className="text-center py-8">
        <span className="text-4xl mb-2 block">💡</span>
        <p className="text-sm text-notely-text-secondary mb-2">No content suggestions available</p>
        <p className="text-xs text-notely-text-tertiary">
          Save more posts to get AI-powered content repurposing ideas
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-xs font-semibold text-notely-text-secondary mb-1 uppercase tracking-wide">
            Content Opportunities
          </h4>
          <p className="text-xs text-notely-text-tertiary">
            {suggestions.length} suggestions from your saved posts
          </p>
        </div>
      </div>

      {/* Suggestions Grid */}
      <div className="space-y-3 max-h-96 overflow-y-auto">
        {suggestions.map((suggestion) => {
          const generated = generatedContent.get(suggestion.id);
          const isGenerating = generated?.isGenerating || false;
          const hasGenerated = generated?.content && !isGenerating;

          return (
            <div
              key={suggestion.id}
              className="bg-notely-surface rounded-notely-md border border-notely-border p-3 hover:bg-notely-card notely-filter-transition"
            >
              {/* Original Post Preview */}
              <div className="flex items-start space-x-3 mb-3">
                <div className="flex-shrink-0">
                  <PlatformLogo 
                    platform={suggestion.originalPost.platform || 'Web'} 
                    className="w-4 h-4 text-notely-text-secondary" 
                  />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-xs text-notely-text-secondary line-clamp-2">
                    {getPostPreview(suggestion.originalPost)}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className={`text-xs ${getConfidenceColor(suggestion.confidence)}`}>
                      {Math.round(suggestion.confidence * 100)}% match
                    </span>
                    {suggestion.estimatedEngagement && (
                      <span className={`text-xs px-2 py-0.5 rounded-full ${getEngagementColor(suggestion.estimatedEngagement)}`}>
                        {suggestion.estimatedEngagement}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              {/* Suggestion */}
              <div className="mb-3">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="text-sm">{suggestion.type === 'thread' ? '🧵' : 
                                            suggestion.type === 'caption' ? '📝' :
                                            suggestion.type === 'newsletter' ? '📧' :
                                            suggestion.type === 'quote' ? '💬' :
                                            suggestion.type === 'poll' ? '📊' : '⭐'}</span>
                  <h5 className="text-sm font-medium text-notely-text-primary">{suggestion.title}</h5>
                </div>
                <p className="text-xs text-notely-text-secondary">{suggestion.description}</p>
              </div>

              {/* Generated Content Preview */}
              {hasGenerated && (
                <div className="mb-3 p-2 bg-notely-bg rounded border border-notely-border">
                  <p className="text-xs text-notely-text-secondary mb-1 font-medium">Generated Content:</p>
                  <p className="text-xs text-notely-text-primary line-clamp-3">
                    {generated.content}
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => handleGenerate(suggestion)}
                  disabled={isGenerating}
                  className="notely-btn-primary text-xs px-3 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed notely-filter-transition hover:scale-105"
                >
                  {isGenerating ? (
                    <span className="flex items-center space-x-1">
                      <div className="animate-spin rounded-full h-3 w-3 border-b border-white"></div>
                      <span>Generating...</span>
                    </span>
                  ) : hasGenerated ? 'Regenerate' : 'Generate'}
                </button>

                {hasGenerated && (
                  <>
                    <button
                      onClick={() => handleSaveAsDraft(suggestion)}
                      className="notely-btn-secondary text-xs px-3 py-1.5 notely-filter-transition hover:scale-105"
                    >
                      Save Draft
                    </button>
                    <button
                      onClick={() => handleSchedule(suggestion)}
                      className="notely-btn-secondary text-xs px-3 py-1.5 notely-filter-transition hover:scale-105"
                    >
                      Schedule
                    </button>
                  </>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Footer */}
      {suggestions.length > 0 && (
        <div className="text-center pt-2 border-t border-notely-border">
          <p className="text-xs text-notely-text-tertiary">
            💡 Tip: High-confidence suggestions work best for repurposing
          </p>
        </div>
      )}
    </div>
  );
};

export default UseWhatYouSavedWidget;
